(function(){
  const LS_KEY_ACK = 'tf_privacy_ack_v1';
  const LS_KEY_EMAIL = 'tf_email_submitted_v1';

  function qs(s, r=document){ return r.querySelector(s); }
  function ce(tag, props={}){ const el = document.createElement(tag); Object.assign(el, props); return el; }
  function openModal(){ backdrop.classList.add('show'); }
  function closeModal(){ backdrop.classList.remove('show'); }

  // inject modal html
  const backdrop = ce('div', { className: 'modal-backdrop', id: 'tf-modal' });
  backdrop.innerHTML = `
    <div class="modal" role="dialog" aria-modal="true" aria-labelledby="tf-modal-title">
      <header>
        <h3 id="tf-modal-title">privacy & updates</h3>
        <button class="close" aria-label="close">×</button>
      </header>
      <div class="body">
        <p>these tools run locally in your browser whenever possible. files aren’t uploaded unless clearly stated. want updates when we add more tools?</p>
        <form id="tf-modal-form">
          <input type="email" id="tf-email" placeholder="email for new tool drops (optional)" />
          <button class="btn primary" type="submit">notify me</button>
        </form>
        <div class="ok-row">
          <input type="checkbox" id="tf-ack" />
          <label for="tf-ack">i’ve read this. don’t show again.</label>
        </div>
        <div class="fine">you can also open this anytime via the “privacy & updates” link in the footer.</div>
      </div>
    </div>
  `;
  document.body.appendChild(backdrop);

  // wire interactions
  backdrop.addEventListener('click', (e)=> { if(e.target === backdrop) closeModal(); });
  qs('.close', backdrop).addEventListener('click', closeModal);

  // footer link injection (if footer exists)
  const footer = document.querySelector('footer.site-footer p');
  if (footer && !footer.dataset.privAppended) {
    footer.innerHTML += ' • <span class="privacy-link" id="tf-open-privacy">privacy & updates</span>';
    footer.dataset.privAppended = '1';
    document.addEventListener('click', (e)=>{
      if (e.target && e.target.id === 'tf-open-privacy') openModal();
    });
  }

  // auto-open after delay if not acknowledged
  if (localStorage.getItem(LS_KEY_ACK) !== '1') {
    setTimeout(openModal, 8000);
  }

  // acknowledge checkbox
  const ack = qs('#tf-ack', backdrop);
  ack.addEventListener('change', (e)=>{
    if (e.target.checked) localStorage.setItem(LS_KEY_ACK, '1');
    else localStorage.removeItem(LS_KEY_ACK);
  });

  // form submit (no backend by default — supports endpoint override)
  const form = qs('#tf-modal-form', backdrop);
  form.addEventListener('submit', async (e)=>{
    e.preventDefault();
    const email = qs('#tf-email', backdrop).value.trim();
    if (!email) { closeModal(); return; }

    // configurable endpoint: set window.TF_EMAIL_ENDPOINT = 'https://formspree.io/f/xxxxxx'
    const ep = window.TF_EMAIL_ENDPOINT || null;
    try {
      if (ep) {
        await fetch(ep, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json', 'Accept': 'application/json' },
          body: JSON.stringify({ email, source: location.pathname })
        });
      } else {
        // fallback: mailto open so user can send themselves; also store locally
        window.location.href = `mailto:<EMAIL>?subject=tool%20farm%20updates&body=subscribe:%20${encodeURIComponent(email)}`;
      }
      localStorage.setItem(LS_KEY_EMAIL, email);
      closeModal();
    } catch (err){
      console.error('email submit error', err);
      closeModal();
    }
  });
})();