<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Bulk Image Resizer – Forgelit</title>
  <link rel="stylesheet" href="../assets/css/style.css" />
  <script defer src="https://cdn.jsdelivr.net/npm/jszip@3.10.1/dist/jszip.min.js"></script>
  <script defer src="https://cdn.jsdelivr.net/npm/file-saver@2.0.5/dist/FileSaver.min.js"></script>
  <script defer src="./script.js"></script>
  <link rel="stylesheet" href="../assets/css/modal.css" />
</head>
<body>
  <header class="site-header">
    <a href="../index.html" class="brand">Forgelit</a>
    <nav>
      <a href="../heic-to-jpg/index.html">HEIC → JPG</a>
      <a href="../exif-cleaner/index.html">EXIF Cleaner</a>
    </nav>
  </header>

  <main class="container">
    <h1>Bulk Image Resizer</h1>
    <p class="sub">Resize many images at once, locally in your browser.</p>

    <div id="dropzone" class="dropzone" tabindex="0" role="button" aria-label="drop files here">
      <input id="file-input" type="file" accept="image/*" multiple hidden />
      <p>Drag & drop images here or <button id="browse-btn" class="btn" type="button">browse</button></p>
      <small>Supports JPG/PNG/WEBP (others may load if your browser can display them)</small>
    </div>

    <div class="card" style="margin-top:14px;">
      <div class="controls" style="gap:14px;">
        <label>Filename Pattern: <input id="pattern" type="text" value="{base}_{w}x{h}" /></label>
        <label>Preset:
          <select id="preset">
            <option value="1080x1080@2x">1080 × 1080 @2x (2160)</option>
            <option value="1920x1080@2x">1920 × 1080 @2x (3840 × 2160)</option>
            <option value="2560x1440@2x">2560 × 1440 @2x (5120 × 2880)</option>
            <option value="3840x2160@2x">3840 × 2160 @2x (7680 × 4320)</option>
            <option value="1080w@2x">1080w @2x (2160w)</option>
            <option value="2048w@2x">2048w @2x (4096w)</option>
            <option value="none">Custom</option>
            <option value="1080x1080">1080 × 1080 (Square)</option>
            <option value="1920x1080">1920 × 1080 (HD)</option>
            <option value="2560x1440">2560 × 1440 (2k)</option>
            <option value="3840x2160">3840 × 2160 (4k)</option>
            <option value="2160w">2160w (Width only, Retina HD)</option>
            <option value="7680x4320">7680 × 4320 (8k)</option>
            <option value="4320w">4320w (Width only, Retina 4k)</option>
            <option value="long-1080">1080 Long edge</option>
            <option value="long-2160">2160 Long edge</option>
            <option value="short-1080">1080 Short Edge</option>
            <option value="short-2160">2160 Short Edge</option>
            <option value="1080w">1080w (Width only)</option>
            <option value="2048w">2048w (Width only)</option>
          </select>
        </label>
        <label>Mode:
          <select id="mode">
            <option value="fit">Fit (Keep aspect inside W×H)</option>
            <option value="long">Long Edge</option>
            <option value="short">Short Edge</option>
            <option value="width">Width Only</option>
            <option value="height">Height Only</option>
            <option value="stretch">Stretch (Ignore Aspect)</option>
          </select>
        </label>
        <label>Width: <input id="width" type="number" min="1" placeholder="e.g. 1920" /></label>
        <label>Height: <input id="height" type="number" min="1" placeholder="e.g. 1080" /></label>
        <label><input id="keep-aspect" type="checkbox" checked /> Keep Aspect</label>
      </div>

      <div class="controls" style="gap:14px;">
        <label>Format:
          <select id="format">
            <option value="jpeg">JPEG</option>
            <option value="png">PNG</option>
            <option value="webp">WEBP</option>
          </select>
        </label>
        <label>Quality (JPEG/WEBP): <input id="quality" type="range" min="0.5" max="1" step="0.05" value="0.9" /> <span id="qv">0.90</span></label>
        <button id="start" class="btn">resize all</button>
        <label>Watermark: <input id="wm-text" type="text" placeholder="text (optional)" /></label>
        <label>Size(px): <input id="wm-size" type="number" min="6" value="32" /></label>
        <label>Opacity: <input id="wm-op" type="range" min="0" max="1" step="0.05" value="0.35" /></label>
        <label>Position:
          <select id="wm-pos">
            <option value="br">Bottom-Right</option>
            <option value="bl">Bottom-Left</option>
            <option value="tr">Top-Right</option>
            <option value="tl">Top-Left</option>
            <option value="c">Center</option>
          </select>
        </label>
        <button id="zip-all" class="btn" disabled>download zip</button>
        <span id="status" class="status"></span>
      </div>
    </div>

    <ul id="results" class="results" aria-live="polite"></ul>

    <section class="privacy">
      <h2>Privacy</h2>
      <p>Processing happens in your browser using canvas. Nothing is uploaded.</p>
    </section>
  </main>

  <footer class="site-footer">
    <p>© 2025 Forgelit • <a href="../index.html">All Tools</a></p>
  </footer>
  <script>window.TF_EMAIL_ENDPOINT="";</script>
  <script src="../assets/js/modal.js" defer></script>
</body>
</html>