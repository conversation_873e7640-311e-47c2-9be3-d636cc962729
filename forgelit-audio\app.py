import os, shutil, tempfile, subprocess
from pathlib import Path
from flask import Flask, request, send_file, render_template, abort
from werkzeug.utils import secure_filename

MAX_CONTENT_LENGTH = 200 * 1024 * 1024
ALLOWED_EXT = {'.wav', '.m4a', '.aac', '.ogg', '.oga', '.flac', '.mp3', '.opus', '.webm', '.wma', '.aif', '.aiff'}
BITRATE = os.getenv('A2M_BITRATE', '320k')

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = MAX_CONTENT_LENGTH

def has_ffmpeg():
    try:
        subprocess.run(['ffmpeg', '-version'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=False)
        return True
    except FileNotFoundError:
        return False

@app.route('/status')
def status():
    try:
        out = subprocess.check_output(['ffmpeg', '-version']).decode('utf-8', errors='ignore').split('\n', 1)[0]
        return {'status': 'ok', 'ffmpeg': out}
    except Exception as e:
        return {'status': 'error', 'error': str(e)}, 500

@app.route('/', methods=['GET'])
def index():
    return render_template('index.html', bitrate=BITRATE, ffmpeg_ok=has_ffmpeg())

def convert_one(in_path: Path, out_dir: Path, bitrate: str) -> Path:
    base = in_path.stem
    out_path = out_dir / f"{base}.mp3"
    cmd = ['ffmpeg', '-y', '-i', str(in_path), '-vn', '-ar', '44100', '-ac', '2', '-b:a', bitrate, str(out_path)]
    proc = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    if proc.returncode != 0:
        raise RuntimeError(proc.stderr.decode('utf-8', errors='ignore')[:5000])
    return out_path

@app.route('/convert', methods=['POST'])
def convert():
    if not has_ffmpeg():
        return abort(500, description='ffmpeg not available in environment')
    files = request.files.getlist('files')
    bitrate = request.form.get('bitrate', BITRATE)
    if not files:
        return abort(400, description='no files uploaded')
    tmp_root = Path(tempfile.mkdtemp(prefix='a2m_'))
    in_dir = tmp_root / 'in'; out_dir = tmp_root / 'out'
    in_dir.mkdir(parents=True, exist_ok=True); out_dir.mkdir(parents=True, exist_ok=True)
    out_paths = []
    try:
        for f in files:
            if not f.filename:
                continue
            name = secure_filename(f.filename)
            dst = in_dir / name
            f.save(dst)
            out_paths.append(convert_one(dst, out_dir, bitrate))
        if not out_paths:
            return abort(400, description='no valid files to convert')
        if len(out_paths) == 1:
            fp = out_paths[0]
            return send_file(fp, as_attachment=True, download_name=fp.name, mimetype='audio/mpeg')
        else:
            import zipfile, io
            mem = io.BytesIO()
            with zipfile.ZipFile(mem, 'w', compression=zipfile.ZIP_DEFLATED) as z:
                for p in out_paths:
                    z.write(p, arcname=p.name)
            mem.seek(0)
            return send_file(mem, as_attachment=True, download_name='converted_mp3.zip', mimetype='application/zip')
    finally:
        try: shutil.rmtree(tmp_root, ignore_errors=True)
        except Exception: pass

@app.errorhandler(413)
def too_large(e): return ('file too large', 413)

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=int(os.getenv('PORT', 8000)))