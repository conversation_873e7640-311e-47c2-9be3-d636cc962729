# forgelit-audio (flask + ffmpeg)

audio/video → mp3 converter. multi-file; returns single mp3 or zip. exposes `/status` for health. deploy on render/railway/fly (docker).

## structure
```
forgelit-audio/
  app.py
  templates/
    index.html
  static/
    css/style.css
    js/app.js
  Dockerfile
  requirements.txt
  Procfile
  README.md
```

## env
- `A2M_BITRATE` (default `320k`)
- `PORT` (default `8000`)

## run locally (docker)
```bash
docker build -t forgelit-audio .
docker run -p 8000:8000 forgelit-audio
# open http://localhost:8000
# health: http://localhost:8000/status
```

## run locally (python)
```bash
# make sure ffmpeg is installed on your machine
pip install -r requirements.txt
python app.py  # http://localhost:8000
```

## deploy (render)
1. new → web service → **docker** → connect repo.
2. env: optional `A2M_BITRATE=320k`.
3. domain: add `audio.forgelit.com` in "custom domains" → follow CNAME instructions.
4. health check: visit `https://audio.forgelit.com/status` → expect `{ "status": "ok", "ffmpeg": "ffmpeg version ..." }`.

## deploy (railway/fly.io)
- same dockerfile works. set port to `8000`. add custom domain `audio.forgelit.com`.
- ensure the platform allows long-running requests (ffmpeg). serverless functions are NOT recommended.

## notes
- max request size: 200mb (tweak `MAX_CONTENT_LENGTH` in `app.py`).
- temp files are cleaned after each request.
- converts audio tracks from video files (drops video stream).

## troubleshooting
- 500 ffmpeg error: open service logs; most often codec issue. ffmpeg handles almost everything.
- long jobs timing out: bump instance class or platform timeouts; avoid serverless.
- if `/status` fails: container doesn't have ffmpeg (check Dockerfile build logs).