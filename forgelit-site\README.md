# forgelit-site (static tools)

minimal static site hosting the client-side tools (heic→jpg, exif cleaner, bulk resizer, etc). deploy on vercel **or** netlify. no build step.

## structure
```
forgelit-site/
  index.html
  heic-to-jpg/
    index.html
    script.js
  exif-cleaner/
    index.html
    script.js
  bulk-resizer/
    index.html
    script.js
  assets/
    css/
      style.css
      modal.css
    js/
      modal.js
    img/
  vercel.json   # if using vercel
  netlify.toml  # if using netlify (pick one, not both)
```

## deploy (vercel)
1. push to github.
2. vercel → "import project" → select repo → **framework: other** → build command: empty → output dir: `.`
3. add domain: `forgelit.com` (and optionally `www.forgelit.com`).
4. dns: follow vercel's records. set redirect `www → apex`.
5. done. changes deploy on every push.

## deploy (netlify)
1. push to github.
2. netlify → "new site from git" → select repo.
3. build command: empty; publish dir: `.`
4. add domain `forgelit.com` → follow dns/cname prompts.
5. done.

## link to audio app
- add a link to `https://audio.forgelit.com/` in `index.html` + footers.

## optional config
- `vercel.json` or `netlify.toml` include sane cache headers (immutable for assets, short for html).
- set `window.TF_EMAIL_ENDPOINT` if wiring the privacy modal email capture.

## troubleshooting
- 404s? ensure file paths are **relative** (e.g., `../assets/...` inside tool folders).
- stale assets? cache is long for `js/css`; bump filenames or `?v=2`.